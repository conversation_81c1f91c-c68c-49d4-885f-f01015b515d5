"use client"
import { useEffect, Suspense } from 'react'
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { DateRangeSelector } from "@/components/dashboard/date-range-selector"
import { PlatformSelector } from "@/components/dashboard/platform-selector"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { DashboardCustomizer, type DashboardWidget } from "@/components/dashboard/dashboard-customizer"
import { KPIOverview } from "@/components/dashboard/kpi-overview"
import { AutomatedInsights } from "@/components/dashboard/automated-insights"
import { LeadGenerationSection } from "@/components/dashboard/lead-generation-section"
import { PPCPerformanceSection } from "@/components/dashboard/ppc-performance-section"
import { ContentPerformanceSection } from "@/components/dashboard/content-performance-section"
import { RecentActivityFeed } from "@/components/dashboard/recent-activity-feed"
import { UpcomingContent } from "@/components/dashboard/upcoming-content"
import { PerformanceWrapper } from '@/components/debug/PerformanceWrapper'
import { StreamingErrorBoundary } from '@/components/debug/StreamingErrorBoundary'
import { useDebugHook, useRenderCount } from '@/lib/debug/useDebugValue'
import { useComponentDebug } from '@/lib/debug/component-debug'
import { logger } from '@/lib/debug/logger'

export default function DashboardPage() {
  // Debug hooks
  const renderCount = useRenderCount('DashboardPage')
  useComponentDebug('DashboardPage', { widgetCount: 7 })

  // Define dashboard widgets
  const dashboardWidgets: DashboardWidget[] = [
    {
      id: "kpi-overview",
      title: "KPI Overview",
      size: "full",
      component: <KPIOverview />,
      locked: true, // This widget cannot be moved
    },
    {
      id: "automated-insights",
      title: "Automated Insights",
      description: "AI-powered insights based on your social media performance",
      size: "full",
      component: <AutomatedInsights />,
    },
    {
      id: "lead-generation",
      title: "Lead Generation",
      size: "medium",
      component: <LeadGenerationSection />,
    },
    {
      id: "ppc-performance",
      title: "PPC Performance",
      size: "medium",
      component: <PPCPerformanceSection />,
    },
    {
      id: "content-performance",
      title: "Content Performance",
      size: "large",
      component: <ContentPerformanceSection />,
    },
    {
      id: "recent-activity",
      title: "Recent Activity",
      description: "Latest updates across your social platforms",
      size: "small",
      component: <RecentActivityFeed />,
    },
    {
      id: "upcoming-content",
      title: "Upcoming Content",
      description: "Scheduled posts for the next 7 days",
      size: "medium",
      component: <UpcomingContent />,
    },
  ]

  useDebugHook(dashboardWidgets.length, 'Widget Count', 'DashboardPage')

  useEffect(() => {
    // Use requestIdleCallback to defer logging and prevent streaming conflicts
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      requestIdleCallback(() => {
        logger.info('Dashboard mounted', {
          widgetCount: dashboardWidgets.length,
          renderCount
        }, 'DashboardPage')
      })
    }
  }, [dashboardWidgets.length, renderCount])

  return (
    <StreamingErrorBoundary>
      <DashboardLayout>
        <PerformanceWrapper id="dashboard-main">
          <div className="flex flex-col gap-6 p-6">
            {/* Debug Info in Development */}
            {process.env.NODE_ENV === 'development' && (
              <div className="text-xs text-gray-500 bg-gray-100 p-2 rounded" suppressHydrationWarning>
                🏠 Dashboard Debug: {dashboardWidgets.length} widgets | Renders: {renderCount}
              </div>
            )}

            <Suspense fallback={<div className="h-16 bg-muted animate-pulse rounded" />}>
              <PerformanceWrapper id="dashboard-header">
                <DashboardHeader />
              </PerformanceWrapper>
            </Suspense>

            <div className="flex flex-wrap items-center justify-between gap-4">
              <Suspense fallback={<div className="h-10 w-32 bg-muted animate-pulse rounded" />}>
                <PerformanceWrapper id="platform-selector">
                  <PlatformSelector />
                </PerformanceWrapper>
              </Suspense>
              <Suspense fallback={<div className="h-10 w-48 bg-muted animate-pulse rounded" />}>
                <PerformanceWrapper id="date-range-selector">
                  <DateRangeSelector />
                </PerformanceWrapper>
              </Suspense>
            </div>

            <Suspense fallback={<div className="h-96 bg-muted animate-pulse rounded" />}>
              <PerformanceWrapper id="dashboard-customizer">
                <DashboardCustomizer widgets={dashboardWidgets} />
              </PerformanceWrapper>
            </Suspense>
          </div>
        </PerformanceWrapper>
      </DashboardLayout>
    </StreamingErrorBoundary>
  )
}
