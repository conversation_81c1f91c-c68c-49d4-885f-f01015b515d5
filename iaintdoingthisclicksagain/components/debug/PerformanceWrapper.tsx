"use client"

import React, { useEffect, useRef, useState, ReactNode } from 'react'
import { logger } from '@/lib/debug/logger'
import { Badge } from '@/components/ui/badge'

interface PerformanceWrapperProps {
  children: ReactNode
  id: string
  threshold?: number // ms - warn if render takes longer
  showMetrics?: boolean // show performance metrics in UI
  logToConsole?: boolean
}

interface PerformanceMetrics {
  renderTime: number
  renderCount: number
  averageRenderTime: number
  maxRenderTime: number
  minRenderTime: number
  lastRenderTime: number
}

export function PerformanceWrapper({
  children,
  id,
  threshold = 16, // 60fps = 16.67ms per frame
  showMetrics = process.env.NODE_ENV === 'development',
  logToConsole = true
}: PerformanceWrapperProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    renderCount: 0,
    averageRenderTime: 0,
    maxRenderTime: 0,
    minRenderTime: Infinity,
    lastRenderTime: 0
  })

  const renderStartTime = useRef<number>(0)
  const renderTimes = useRef<number[]>([])
  const maxStoredRenders = 100

  // Start timing before render
  renderStartTime.current = performance.now()

  useEffect(() => {
    // Only run performance tracking on client side to prevent streaming issues
    if (typeof window === 'undefined') return

    // Calculate render time after render completes
    const renderTime = performance.now() - renderStartTime.current
    renderTimes.current.push(renderTime)

    // Keep only recent render times
    if (renderTimes.current.length > maxStoredRenders) {
      renderTimes.current = renderTimes.current.slice(-maxStoredRenders)
    }

    const newMetrics: PerformanceMetrics = {
      renderTime,
      renderCount: metrics.renderCount + 1,
      averageRenderTime: renderTimes.current.reduce((sum, time) => sum + time, 0) / renderTimes.current.length,
      maxRenderTime: Math.max(metrics.maxRenderTime, renderTime),
      minRenderTime: Math.min(metrics.minRenderTime === Infinity ? renderTime : metrics.minRenderTime, renderTime),
      lastRenderTime: renderTime
    }

    setMetrics(newMetrics)

    // Use requestIdleCallback to defer logging and prevent blocking
    if (logToConsole && 'requestIdleCallback' in window) {
      requestIdleCallback(() => {
        if (renderTime > threshold) {
          logger.warn(`Slow render detected in ${id}`, {
            renderTime: `${renderTime.toFixed(2)}ms`,
            threshold: `${threshold}ms`,
            renderCount: newMetrics.renderCount
          }, 'PerformanceWrapper')
        } else {
          logger.performance(`Render completed: ${id}`, renderStartTime.current, 'PerformanceWrapper')
        }
      })
    }

    // Dispatch custom event for debug panel with throttling
    if (typeof window !== 'undefined' && newMetrics.renderCount % 5 === 0) {
      // Only dispatch every 5th render to reduce event spam
      requestIdleCallback(() => {
        window.dispatchEvent(new CustomEvent('performance-metric', {
          detail: {
            componentId: id,
            metrics: newMetrics
          }
        }))
      })
    }
  })

  const getPerformanceColor = (time: number) => {
    if (time > threshold * 2) return 'destructive'
    if (time > threshold) return 'secondary'
    return 'default'
  }

  const formatTime = (time: number) => `${time.toFixed(2)}ms`

  return (
    <div className="relative">
      {children}
      
      {showMetrics && metrics.renderCount > 0 && (
        <div className="absolute top-0 right-0 z-50 p-1">
          <div className="flex flex-wrap gap-1 text-xs">
            <Badge 
              variant={getPerformanceColor(metrics.lastRenderTime)}
              className="text-xs"
              title={`Last render: ${formatTime(metrics.lastRenderTime)}`}
            >
              {formatTime(metrics.lastRenderTime)}
            </Badge>
            
            {metrics.renderCount > 1 && (
              <>
                <Badge 
                  variant="outline" 
                  className="text-xs"
                  title={`Average render time: ${formatTime(metrics.averageRenderTime)}`}
                >
                  Avg: {formatTime(metrics.averageRenderTime)}
                </Badge>
                
                <Badge 
                  variant="outline" 
                  className="text-xs"
                  title={`Render count: ${metrics.renderCount}`}
                >
                  #{metrics.renderCount}
                </Badge>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * HOC version of PerformanceWrapper
 */
export function withPerformanceWrapper<P extends object>(
  Component: React.ComponentType<P>,
  id: string,
  options?: Omit<PerformanceWrapperProps, 'children' | 'id'>
) {
  const WrappedComponent = (props: P) => (
    <PerformanceWrapper id={id} {...options}>
      <Component {...props} />
    </PerformanceWrapper>
  )

  WrappedComponent.displayName = `withPerformanceWrapper(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

/**
 * Hook to get performance metrics for a component
 */
export function usePerformanceMetrics(componentId: string) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)

  useEffect(() => {
    const handlePerformanceMetric = (event: CustomEvent) => {
      if (event.detail.componentId === componentId) {
        setMetrics(event.detail.metrics)
      }
    }

    window.addEventListener('performance-metric', handlePerformanceMetric as EventListener)
    
    return () => {
      window.removeEventListener('performance-metric', handlePerformanceMetric as EventListener)
    }
  }, [componentId])

  return metrics
}
